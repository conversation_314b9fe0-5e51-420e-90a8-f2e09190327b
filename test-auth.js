const http = require('http');

function makeRequest(options, postData) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          data: data
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (postData) {
      req.write(postData);
    }
    req.end();
  });
}

async function testAuth() {
  try {
    console.log('Testing authentication endpoints...');

    // Test health endpoint first
    console.log('\n1. Testing health endpoint...');
    const healthOptions = {
      hostname: 'localhost',
      port: 5006,
      path: '/health',
      method: 'GET'
    };

    const healthResponse = await makeRequest(healthOptions);
    console.log('Health check status:', healthResponse.status);
    console.log('Health check data:', healthResponse.data);

    // Test login endpoint
    console.log('\n2. Testing login endpoint...');
    const loginData = JSON.stringify({
      email: '<EMAIL>',
      password: 'Test123',
      userType: 'farmer'
    });

    const loginOptions = {
      hostname: 'localhost',
      port: 5006,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(loginData)
      }
    };

    const loginResponse = await makeRequest(loginOptions, loginData);
    console.log('Login response status:', loginResponse.status);
    console.log('Login response:', loginResponse.data);

  } catch (error) {
    console.error('Error testing auth:', error);
  }
}

testAuth();
